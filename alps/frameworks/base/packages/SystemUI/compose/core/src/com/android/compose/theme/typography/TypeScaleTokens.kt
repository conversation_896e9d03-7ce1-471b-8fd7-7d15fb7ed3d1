/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.compose.theme.typography

import androidx.compose.ui.unit.sp

internal class TypeScaleTokens(typefaceTokens: TypefaceTokens) {
    val bodyLargeFont = typefaceTokens.plain
    val bodyLargeLineHeight = 24.0.sp
    val bodyLargeSize = 16.sp
    val bodyLargeTracking = 0.0.sp
    val bodyLargeWeight = TypefaceTokens.WeightRegular
    val bodyMediumFont = typefaceTokens.plain
    val bodyMediumLineHeight = 20.0.sp
    val bodyMediumSize = 14.sp
    val bodyMediumTracking = 0.0.sp
    val bodyMediumWeight = TypefaceTokens.WeightRegular
    val bodySmallFont = typefaceTokens.plain
    val bodySmallLineHeight = 16.0.sp
    val bodySmallSize = 12.sp
    val bodySmallTracking = 0.1.sp
    val bodySmallWeight = TypefaceTokens.WeightRegular
    val displayLargeFont = typefaceTokens.brand
    val displayLargeLineHeight = 64.0.sp
    val displayLargeSize = 57.sp
    val displayLargeTracking = 0.0.sp
    val displayLargeWeight = TypefaceTokens.WeightRegular
    val displayMediumFont = typefaceTokens.brand
    val displayMediumLineHeight = 52.0.sp
    val displayMediumSize = 45.sp
    val displayMediumTracking = 0.0.sp
    val displayMediumWeight = TypefaceTokens.WeightRegular
    val displaySmallFont = typefaceTokens.brand
    val displaySmallLineHeight = 44.0.sp
    val displaySmallSize = 36.sp
    val displaySmallTracking = 0.0.sp
    val displaySmallWeight = TypefaceTokens.WeightRegular
    val headlineLargeFont = typefaceTokens.brand
    val headlineLargeLineHeight = 40.0.sp
    val headlineLargeSize = 32.sp
    val headlineLargeTracking = 0.0.sp
    val headlineLargeWeight = TypefaceTokens.WeightRegular
    val headlineMediumFont = typefaceTokens.brand
    val headlineMediumLineHeight = 36.0.sp
    val headlineMediumSize = 28.sp
    val headlineMediumTracking = 0.0.sp
    val headlineMediumWeight = TypefaceTokens.WeightRegular
    val headlineSmallFont = typefaceTokens.brand
    val headlineSmallLineHeight = 32.0.sp
    val headlineSmallSize = 24.sp
    val headlineSmallTracking = 0.0.sp
    val headlineSmallWeight = TypefaceTokens.WeightRegular
    val labelLargeFont = typefaceTokens.plain
    val labelLargeLineHeight = 20.0.sp
    val labelLargeSize = 14.sp
    val labelLargeTracking = 0.0.sp
    val labelLargeWeight = TypefaceTokens.WeightMedium
    val labelMediumFont = typefaceTokens.plain
    val labelMediumLineHeight = 16.0.sp
    val labelMediumSize = 12.sp
    val labelMediumTracking = 0.1.sp
    val labelMediumWeight = TypefaceTokens.WeightMedium
    val labelSmallFont = typefaceTokens.plain
    val labelSmallLineHeight = 16.0.sp
    val labelSmallSize = 11.sp
    val labelSmallTracking = 0.1.sp
    val labelSmallWeight = TypefaceTokens.WeightMedium
    val titleLargeFont = typefaceTokens.brand
    val titleLargeLineHeight = 28.0.sp
    val titleLargeSize = 22.sp
    val titleLargeTracking = 0.0.sp
    val titleLargeWeight = TypefaceTokens.WeightRegular
    val titleMediumFont = typefaceTokens.plain
    val titleMediumLineHeight = 24.0.sp
    val titleMediumSize = 16.sp
    val titleMediumTracking = 0.0.sp
    val titleMediumWeight = TypefaceTokens.WeightMedium
    val titleSmallFont = typefaceTokens.plain
    val titleSmallLineHeight = 20.0.sp
    val titleSmallSize = 14.sp
    val titleSmallTracking = 0.0.sp
    val titleSmallWeight = TypefaceTokens.WeightMedium
}
